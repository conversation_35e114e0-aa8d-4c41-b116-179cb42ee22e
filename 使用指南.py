#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天气爬虫工具使用指南和示例
"""

import os
from weather_scraper import WeatherScraper
from update_weather_excel import WeatherExcelUpdater

def show_menu():
    """显示菜单"""
    print("\n" + "="*50)
    print("🌤️  天气数据爬取工具")
    print("="*50)
    print("1. 爬取单个城市天气数据")
    print("2. 爬取多个城市天气数据") 
    print("3. 更新现有Excel文件")
    print("4. 向Excel文件添加新城市")
    print("5. 查看支持的城市列表")
    print("6. 批量爬取多个月份数据")
    print("0. 退出")
    print("="*50)

def get_supported_cities():
    """获取支持的城市列表"""
    from update_weather_excel import CITY_MAPPING
    return CITY_MAPPING

def option_1():
    """爬取单个城市天气数据"""
    print("\n📍 爬取单个城市天气数据")
    
    city = input("请输入城市名称（拼音，如jiaxing）: ").strip()
    year = int(input("请输入年份（如2024）: "))
    month = int(input("请输入月份（如3）: "))
    output = input("请输入输出文件名（如天气数据.xlsx）: ").strip()
    
    scraper = WeatherScraper()
    data = scraper.get_weather_data(city, year, month)
    scraper.save_to_excel(data, output)

def option_2():
    """爬取多个城市天气数据"""
    print("\n🏙️ 爬取多个城市天气数据")
    
    cities_input = input("请输入城市名称（拼音，用空格分隔，如: jiaxing hangzhou ningbo）: ").strip()
    cities = cities_input.split()
    year = int(input("请输入年份（如2024）: "))
    month = int(input("请输入月份（如3）: "))
    output = input("请输入输出文件名（如多城市天气.xlsx）: ").strip()
    
    scraper = WeatherScraper()
    scraper.scrape_multiple_cities(cities, year, month, output)

def option_3():
    """更新现有Excel文件"""
    print("\n📊 更新现有Excel文件")
    
    excel_file = input("请输入Excel文件路径（如八月地方天气.xlsx）: ").strip()
    if not os.path.exists(excel_file):
        print(f"❌ 文件 {excel_file} 不存在！")
        return
    
    year = int(input("请输入年份（如2024）: "))
    month = int(input("请输入月份（如3）: "))
    output = input("请输入输出文件名（回车使用原文件名）: ").strip()
    output = output if output else None
    
    updater = WeatherExcelUpdater()
    updater.update_excel_file(excel_file, year, month, output)

def option_4():
    """向Excel文件添加新城市"""
    print("\n➕ 向Excel文件添加新城市")
    
    excel_file = input("请输入Excel文件路径（如八月地方天气.xlsx）: ").strip()
    if not os.path.exists(excel_file):
        print(f"❌ 文件 {excel_file} 不存在！")
        return
    
    cities_input = input("请输入要添加的城市名称（中文，用空格分隔，如: 嘉兴 绍兴）: ").strip()
    new_cities = cities_input.split()
    year = int(input("请输入年份（如2024）: "))
    month = int(input("请输入月份（如3）: "))
    output = input("请输入输出文件名（如更新后的天气.xlsx）: ").strip()
    
    updater = WeatherExcelUpdater()
    updater.add_cities_to_excel(excel_file, new_cities, year, month, output)

def option_5():
    """查看支持的城市列表"""
    print("\n🗺️ 支持的城市列表")
    cities = get_supported_cities()
    
    print("\n中文名 -> 拼音名")
    print("-" * 30)
    for chinese, pinyin in cities.items():
        print(f"{chinese:6} -> {pinyin}")
    
    print(f"\n总共支持 {len(cities)} 个城市")

def option_6():
    """批量爬取多个月份数据"""
    print("\n📅 批量爬取多个月份数据")
    
    city = input("请输入城市名称（拼音，如jiaxing）: ").strip()
    year = int(input("请输入年份（如2024）: "))
    start_month = int(input("请输入开始月份（如1）: "))
    end_month = int(input("请输入结束月份（如3）: "))
    output = input("请输入输出文件名（如年度天气数据.xlsx）: ").strip()
    
    scraper = WeatherScraper()
    all_data = []
    
    for month in range(start_month, end_month + 1):
        print(f"\n正在爬取 {year}年{month}月数据...")
        month_data = scraper.get_weather_data(city, year, month)
        all_data.extend(month_data)
    
    scraper.save_to_excel(all_data, output)

def main():
    """主函数"""
    while True:
        show_menu()
        
        try:
            choice = input("\n请选择功能（0-6）: ").strip()
            
            if choice == '0':
                print("👋 再见！")
                break
            elif choice == '1':
                option_1()
            elif choice == '2':
                option_2()
            elif choice == '3':
                option_3()
            elif choice == '4':
                option_4()
            elif choice == '5':
                option_5()
            elif choice == '6':
                option_6()
            else:
                print("❌ 无效选择，请输入0-6之间的数字")
                
        except KeyboardInterrupt:
            print("\n\n👋 用户取消操作，再见！")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")
            print("请检查输入是否正确")

if __name__ == "__main__":
    main()
